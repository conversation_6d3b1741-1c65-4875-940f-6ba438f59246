import React, { useState, useCallback, useMemo, useEffect } from "react";
import {
  Autocomplete,
  TextField,
  CircularProgress,
  FormControl,
  Box,
  Typography,
  Chip,
} from "@mui/material";
import { debounce } from "lodash";
import { useUser } from "@/src/api/hooks/user/use-user";

interface ReferrerSearchInputProps {
  value: any;
  onChange: (value: any) => void;
  storeId: string;
  error?: boolean;
  helperText?: string;
  placeholder?: string;
  initialReferrerCode?: string;
}

const ReferrerSearchInput: React.FC<ReferrerSearchInputProps> = ({
  value,
  onChange,
  storeId,
  error = false,
  helperText = "",
  placeholder = "Nhập tên hoặc số điện thoại để tìm kiếm...",
  initialReferrerCode,
}) => {
  const { listUser } = useUser();

  // States
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Function to search referrers
  const searchReferrers = useCallback(
    async (searchText: string) => {
      if (!storeId || !searchText.trim()) {
        setOptions([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setHasSearched(true);

      try {
        const queryData = `?skip=0&limit=20`;
        const bodyData = {
          shopId: storeId,
          search: searchText.trim(),
        };

        const response = await listUser(queryData, bodyData);
        if (response && response.data) {
          const users = response.data.data || [];
          setOptions(users);
        }
      } catch (error) {
        console.error("Error searching referrers:", error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    },
    [storeId]
  );

  // Debounced search function
  const debouncedSearchReferrers = useMemo(
    () =>
      debounce((searchText: string) => {
        searchReferrers(searchText);
      }, 500), // Tăng delay lên 500ms để giảm số lần gọi API
    [searchReferrers]
  );

  // Load initial referrer if provided
  useEffect(() => {
    if (initialReferrerCode && !value && storeId) {
      // Tìm kiếm người giới thiệu ban đầu
      searchReferrers(initialReferrerCode);
    }
  }, [initialReferrerCode, value, storeId, searchReferrers]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearchReferrers.cancel();
    };
  }, [debouncedSearchReferrers]);

  // Handle input change
  const handleInputChange = (event: any, newInputValue: string) => {
    setInputValue(newInputValue);

    if (newInputValue.trim().length >= 2) {
      // Chỉ tìm kiếm khi nhập ít nhất 2 ký tự
      debouncedSearchReferrers(newInputValue);
    } else {
      // Clear options khi input quá ngắn
      setOptions([]);
      setHasSearched(false);
      debouncedSearchReferrers.cancel();
    }
  };

  // Handle selection change
  const handleSelectionChange = (event: any, newValue: any) => {
    onChange(newValue);
    if (newValue) {
      // Khi đã chọn, clear options để không hiển thị dropdown
      setOptions([]);
      setInputValue("");
      setHasSearched(false);
    }
  };

  // Convert phone number format
  const convertPhoneNumber = (phoneNumber: string) => {
    if (phoneNumber?.startsWith("+84")) {
      return phoneNumber.replace("+84", "0");
    }
    return phoneNumber;
  };

  // Determine what to show in input
  const getDisplayValue = () => {
    if (value) {
      return `${value.fullname || "N/A"} (${convertPhoneNumber(value.phoneNumber)})`;
    }
    return "";
  };

  return (
    <FormControl fullWidth margin="dense">
      <Autocomplete
        options={options}
        value={value}
        inputValue={value ? getDisplayValue() : inputValue}
        loading={loading}
        freeSolo={false}
        clearOnBlur={false}
        selectOnFocus={false}
        handleHomeEndKeys={false}
        getOptionLabel={(option) => {
          if (typeof option === "string") return option;
          return option.fullname
            ? `${option.fullname} (${convertPhoneNumber(option.phoneNumber)})`
            : `${convertPhoneNumber(option.phoneNumber)}`;
        }}
        isOptionEqualToValue={(option, value) => option?.referralCode === value?.referralCode}
        onInputChange={handleInputChange}
        onChange={handleSelectionChange}
        renderInput={(params) => (
          <TextField
            {...params}
            error={error}
            helperText={helperText}
            variant="outlined"
            fullWidth
            margin="dense"
            placeholder={value ? "" : placeholder}
            InputProps={{
              ...params.InputProps,
              readOnly: !!value, // Chỉ đọc khi đã có value
              endAdornment: (
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  {loading && <CircularProgress color="inherit" size={20} />}
                  {params.InputProps.endAdornment}
                </Box>
              ),
            }}
          />
        )}
        renderOption={(props, option) => (
          <Box component="li" {...props}>
            <Box sx={{ display: "flex", flexDirection: "column", width: "100%" }}>
              <Typography variant="body2">{option.fullname || "N/A"}</Typography>
              <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <Typography variant="caption" color="text.secondary">
                  {convertPhoneNumber(option.phoneNumber)}
                </Typography>
                <Chip label={option.referralCode} size="small" />
              </Box>
            </Box>
          </Box>
        )}
        noOptionsText={
          !hasSearched ? (
            <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: "center" }}>
              Nhập tên hoặc số điện thoại để tìm kiếm
            </Typography>
          ) : inputValue.trim().length < 2 ? (
            <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: "center" }}>
              Nhập ít nhất 2 ký tự để tìm kiếm
            </Typography>
          ) : (
            <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: "center" }}>
              Không tìm thấy kết quả cho "{inputValue}"
            </Typography>
          )
        }
        // Chỉ mở dropdown khi có options
        open={options.length > 0 && !value}
      />
    </FormControl>
  );
};

export default ReferrerSearchInput;
