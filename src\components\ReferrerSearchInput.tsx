import React, { useState, useCallback, useMemo, useEffect } from "react";
import {
  Autocomplete,
  TextField,
  CircularProgress,
  FormControl,
  Box,
  Typography,
  Chip,
  Avatar,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
} from "@mui/material";
import { Person, Phone, Badge } from "@mui/icons-material";
import { debounce } from "lodash";
import { useUser } from "@/src/api/hooks/user/use-user";

interface ReferrerSearchInputProps {
  value: any;
  onChange: (value: any) => void;
  storeId: string;
  error?: boolean;
  helperText?: string;
  placeholder?: string;
  initialReferrerCode?: string;
}

const ReferrerSearchInput: React.FC<ReferrerSearchInputProps> = ({
  value,
  onChange,
  storeId,
  error = false,
  helperText = "",
  placeholder = "Nhập tên hoặc số điện thoại để tìm kiếm...",
  initialReferrerCode,
}) => {
  const { listUser } = useUser();

  // States
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Function to search referrers
  const searchReferrers = useCallback(
    async (searchText: string) => {
      if (!storeId || !searchText.trim()) {
        setOptions([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setHasSearched(true);

      try {
        const queryData = `?skip=0&limit=20`;
        const bodyData = {
          shopId: storeId,
          search: searchText.trim(),
        };

        const response = await listUser(queryData, bodyData);
        if (response && response.data) {
          const users = response.data.data || [];
          setOptions(users);
        }
      } catch (error) {
        console.error("Error searching referrers:", error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    },
    [storeId]
  );

  // Debounced search function
  const debouncedSearchReferrers = useMemo(
    () =>
      debounce((searchText: string) => {
        searchReferrers(searchText);
      }, 500), // Tăng delay lên 500ms để giảm số lần gọi API
    [searchReferrers]
  );

  // Load initial referrer if provided
  useEffect(() => {
    if (initialReferrerCode && !value && storeId) {
      // Tìm kiếm người giới thiệu ban đầu và set luôn vào value
      const loadInitialReferrer = async () => {
        setLoading(true);
        try {
          const queryData = `?skip=0&limit=20`;
          const bodyData = {
            shopId: storeId,
            search: initialReferrerCode.trim(),
          };

          const response = await listUser(queryData, bodyData);
          if (response && response.data) {
            const users = response.data.data || [];
            // Tìm người giới thiệu có mã khớp chính xác
            const foundReferrer = users.find((user) => user.referralCode === initialReferrerCode);
            if (foundReferrer) {
              // Set luôn vào value, không hiển thị dropdown
              onChange(foundReferrer);
            }
          }
        } catch (error) {
          console.error("Error loading initial referrer:", error);
        } finally {
          setLoading(false);
        }
      };

      loadInitialReferrer();
    }
  }, [initialReferrerCode, value, storeId, onChange]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearchReferrers.cancel();
    };
  }, [debouncedSearchReferrers]);

  // Handle input change
  const handleInputChange = (_event: any, newInputValue: string) => {
    // Nếu đã có value (đã chọn người giới thiệu), không cho phép thay đổi input
    if (value) return;

    setInputValue(newInputValue);

    if (newInputValue.trim().length >= 2) {
      // Chỉ tìm kiếm khi nhập ít nhất 2 ký tự
      debouncedSearchReferrers(newInputValue);
    } else {
      // Clear options khi input quá ngắn
      setOptions([]);
      setHasSearched(false);
      debouncedSearchReferrers.cancel();
    }
  };

  // Handle selection change
  const handleSelectionChange = (_event: any, newValue: any) => {
    onChange(newValue);
    if (newValue) {
      // Khi đã chọn, clear options để không hiển thị dropdown
      setOptions([]);
      setInputValue("");
      setHasSearched(false);
    }
  };

  // Convert phone number format
  const convertPhoneNumber = (phoneNumber: string) => {
    if (phoneNumber?.startsWith("+84")) {
      return phoneNumber.replace("+84", "0");
    }
    return phoneNumber;
  };

  // Determine what to show in input
  const getDisplayValue = () => {
    if (value) {
      return `${value.fullname || "N/A"} (${convertPhoneNumber(value.phoneNumber)})`;
    }
    return "";
  };

  return (
    <FormControl fullWidth margin="dense">
      <Autocomplete
        options={options}
        value={value}
        inputValue={value ? getDisplayValue() : inputValue}
        loading={loading}
        freeSolo={false}
        clearOnBlur={false}
        selectOnFocus={false}
        handleHomeEndKeys={false}
        ListboxProps={{
          sx: {
            maxHeight: 300,
            "& .MuiAutocomplete-option": {
              padding: 0,
            },
          },
        }}
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
              border: "1px solid #e0e0e0",
              mt: 1,
            },
          },
        }}
        getOptionLabel={(option) => {
          if (typeof option === "string") return option;
          return option.fullname
            ? `${option.fullname} (${convertPhoneNumber(option.phoneNumber)})`
            : `${convertPhoneNumber(option.phoneNumber)}`;
        }}
        isOptionEqualToValue={(option, value) => option?.referralCode === value?.referralCode}
        onInputChange={handleInputChange}
        onChange={handleSelectionChange}
        renderInput={(params) => (
          <TextField
            {...params}
            error={error}
            helperText={helperText}
            variant="outlined"
            fullWidth
            margin="dense"
            placeholder={value ? "" : placeholder}
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 2,
                backgroundColor: value ? "#f8f9fa" : "white",
                "&:hover": {
                  backgroundColor: value ? "#f8f9fa" : "#fafafa",
                },
                "&.Mui-focused": {
                  backgroundColor: "white",
                },
              },
              "& .MuiInputBase-input": {
                fontSize: "14px",
                padding: "12px 14px",
              },
            }}
            InputProps={{
              ...params.InputProps,
              readOnly: !!value,
              startAdornment: (
                <Person
                  sx={{
                    color: value ? "primary.main" : "text.secondary",
                    mr: 1,
                    fontSize: 20,
                  }}
                />
              ),
              endAdornment: (
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  {loading && <CircularProgress color="primary" size={18} sx={{ mr: 1 }} />}
                  {params.InputProps.endAdornment}
                </Box>
              ),
            }}
          />
        )}
        renderOption={(props, option) => (
          <Box
            component="li"
            {...props}
            sx={{
              padding: "12px 16px !important",
              borderBottom: "1px solid #f0f0f0",
              "&:hover": {
                backgroundColor: "#f8f9fa",
              },
              "&:last-child": {
                borderBottom: "none",
              },
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", width: "100%", gap: 2 }}>
              <Avatar
                sx={{
                  width: 40,
                  height: 40,
                  backgroundColor: "primary.light",
                  color: "primary.contrastText",
                  fontSize: "14px",
                  fontWeight: 600,
                }}
              >
                {(option.fullname || "U").charAt(0).toUpperCase()}
              </Avatar>

              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color: "text.primary",
                    mb: 0.5,
                  }}
                >
                  {option.fullname || "Chưa có tên"}
                </Typography>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Phone sx={{ fontSize: 14, color: "text.secondary" }} />
                  <Typography
                    variant="caption"
                    sx={{
                      color: "text.secondary",
                      fontFamily: "monospace",
                    }}
                  >
                    {convertPhoneNumber(option.phoneNumber)}
                  </Typography>
                </Box>
              </Box>

              <Chip
                label={option.referralCode}
                size="small"
                sx={{
                  backgroundColor: "primary.main",
                  color: "white",
                  fontWeight: 500,
                  fontSize: "11px",
                  height: 24,
                  "& .MuiChip-label": {
                    px: 1,
                  },
                }}
              />
            </Box>
          </Box>
        )}
        noOptionsText={
          <Box
            sx={{
              p: 3,
              textAlign: "center",
              backgroundColor: "#fafafa",
              borderRadius: 1,
              m: 1,
            }}
          >
            {!hasSearched ? (
              <>
                <Person sx={{ fontSize: 40, color: "text.disabled", mb: 1 }} />
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                  Nhập tên hoặc số điện thoại để tìm kiếm
                </Typography>
                <Typography variant="caption" color="text.disabled">
                  Ít nhất 2 ký tự
                </Typography>
              </>
            ) : inputValue.trim().length < 2 ? (
              <>
                <Person sx={{ fontSize: 40, color: "warning.main", mb: 1 }} />
                <Typography variant="body2" color="warning.main" sx={{ fontWeight: 500 }}>
                  Nhập ít nhất 2 ký tự để tìm kiếm
                </Typography>
              </>
            ) : (
              <>
                <Person sx={{ fontSize: 40, color: "text.disabled", mb: 1 }} />
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                  Không tìm thấy kết quả
                </Typography>
                <Typography variant="caption" color="text.disabled">
                  Thử tìm kiếm với từ khóa khác: "{inputValue}"
                </Typography>
              </>
            )}
          </Box>
        }
        // Chỉ mở dropdown khi có options và chưa chọn value và đang có input
        open={options.length > 0 && !value && inputValue.trim().length >= 2}
      />
    </FormControl>
  );
};

export default ReferrerSearchInput;
