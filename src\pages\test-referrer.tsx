import React, { useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Divider,
  Container,
} from "@mui/material";
import { Refresh, CheckCircle } from "@mui/icons-material";
import ReferrerSearchInput from "@/src/components/ReferrerSearchInput";
import { useStoreId } from "@/src/hooks/use-store-id";

const TestReferrerPage = () => {
  const [selectedReferrer, setSelectedReferrer] = useState(null);
  const storeId = useStoreId();

  const handleClear = () => {
    setSelectedReferrer(null);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: "primary.main" }}>
          🎨 Referrer Search Input - Thiết kế mới
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Component tìm kiếm người giới thiệu với thiết kế đẹp và UX tối ưu
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Demo Component */}
        <Grid item xs={12} md={8}>
          <Card elevation={2} sx={{ borderRadius: 3 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
                🔍 Demo Component
              </Typography>

              <ReferrerSearchInput
                value={selectedReferrer}
                onChange={(newValue) => {
                  console.log("Selected referrer:", newValue);
                  setSelectedReferrer(newValue);
                }}
                storeId={storeId}
                placeholder="Nhập tên hoặc số điện thoại để tìm kiếm..."
              />

              {selectedReferrer && (
                <Box sx={{ mt: 3 }}>
                  <Card
                    sx={{
                      backgroundColor: "success.light",
                      borderRadius: 2,
                      border: "1px solid",
                      borderColor: "success.main",
                    }}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                        <CheckCircle sx={{ color: "success.main", fontSize: 20 }} />
                        <Typography
                          variant="subtitle2"
                          sx={{ fontWeight: 600, color: "success.dark" }}
                        >
                          Đã chọn người giới thiệu
                        </Typography>
                      </Box>
                      <Typography variant="body2" sx={{ color: "success.dark", mb: 1 }}>
                        <strong>Tên:</strong> {selectedReferrer.fullname}
                      </Typography>
                      <Typography variant="body2" sx={{ color: "success.dark", mb: 1 }}>
                        <strong>SĐT:</strong> {selectedReferrer.phoneNumber}
                      </Typography>
                      <Typography variant="body2" sx={{ color: "success.dark" }}>
                        <strong>Mã giới thiệu:</strong> {selectedReferrer.referralCode}
                      </Typography>
                    </CardContent>
                  </Card>

                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={handleClear}
                    sx={{ mt: 2 }}
                  >
                    Xóa để test lại
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Features */}
        <Grid item xs={12} md={4}>
          <Card elevation={2} sx={{ borderRadius: 3, height: "fit-content" }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: "primary.main" }}>
                ✨ Tính năng mới
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  🎨 Thiết kế đẹp:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: "13px" }}>
                  • Icon người dùng ở đầu input
                  <br />
                  • Avatar với chữ cái đầu tên
                  <br />
                  • Chip màu xanh cho mã giới thiệu
                  <br />
                  • Hover effects mượt mà
                  <br />• Shadow và border radius hiện đại
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  🚀 UX tối ưu:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: "13px" }}>
                  • Loading indicator đẹp
                  <br />
                  • Empty states có icon và text
                  <br />
                  • Readonly mode khi đã chọn
                  <br />
                  • Responsive design
                  <br />• Debounce 500ms
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  📱 Responsive:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: "13px" }}>
                  • Hoạt động tốt trên mobile
                  <br />
                  • Touch-friendly
                  <br />• Adaptive sizing
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Divider sx={{ my: 4 }} />

      {/* Instructions */}
      <Card elevation={1} sx={{ borderRadius: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
            📋 Hướng dẫn test
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <Box sx={{ textAlign: "center", p: 2 }}>
                <Typography variant="h6" sx={{ color: "primary.main", mb: 1 }}>
                  1️⃣
                </Typography>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Trạng thái ban đầu
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: "13px" }}>
                  Input trống với icon người dùng, placeholder hướng dẫn
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ textAlign: "center", p: 2 }}>
                <Typography variant="h6" sx={{ color: "warning.main", mb: 1 }}>
                  2️⃣
                </Typography>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Nhập tìm kiếm
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: "13px" }}>
                  Nhập tên hoặc SĐT (ít nhất 2 ký tự), hiển thị loading
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ textAlign: "center", p: 2 }}>
                <Typography variant="h6" sx={{ color: "info.main", mb: 1 }}>
                  3️⃣
                </Typography>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Xem kết quả
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: "13px" }}>
                  Dropdown đẹp với avatar, tên, SĐT và mã giới thiệu
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ textAlign: "center", p: 2 }}>
                <Typography variant="h6" sx={{ color: "success.main", mb: 1 }}>
                  4️⃣
                </Typography>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Sau khi chọn
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: "13px" }}>
                  Input readonly, hiển thị thông tin đã chọn
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
};

export default TestReferrerPage;
